<!doctype html>
<html lang="cs">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="icon" type="image/svg+xml" href="/images/wood_texture.jpeg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    
    <!-- Basic fallback meta tags (will be overridden by SEO component) -->
    <title>Truhlářství Ing. <PERSON><PERSON> - Výroba nábytku z masivního dřeva</title>
    <meta name="description" content="Truhlářství Ing. <PERSON><PERSON> - specializujeme se na výrobu kvalitního nábytku a dřevěných doplňků z masivního dřeva. Zakázkové truhlářství s důrazem na kvalitu a řemeslnou preciznost." />

    <!-- Preload critical resources for performance -->
    <link rel="preload" href="/images-optimized/wood_texture-hero-mobile.webp" as="image" media="(max-width: 767px)" fetchpriority="high" />
    <link rel="preload" href="/images-optimized/wood_texture-hero-tablet.webp" as="image" media="(min-width: 768px) and (max-width: 1023px)" fetchpriority="high" />
    <link rel="preload" href="/images-optimized/wood_texture-hero-desktop.webp" as="image" media="(min-width: 1024px)" fetchpriority="high" />
    
    <!-- DNS prefetch for external services -->
    <link rel="dns-prefetch" href="//formspree.io" />
    
    <!-- Defer non-critical resources -->
    <link rel="prefetch" href="/images.json" as="fetch" crossorigin />
    
    <!-- Critical CSS inlining hint -->
    <style>
      /* Critical above-the-fold styles for faster FCP */
      body { 
        margin: 0; 
        font-family: system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
        line-height: 1.5;
        -webkit-font-smoothing: antialiased;
      }
      .min-h-screen { min-height: 100vh; }
      .flex { display: flex; }
      .flex-col { flex-direction: column; }
      .flex-grow { flex-grow: 1; }
      .relative { position: relative; }
      .absolute { position: absolute; }
      .inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
      .z-0 { z-index: 0; }
      .z-10 { z-index: 10; }
      .h-screen { height: 100vh; }
      .min-h-screen { min-height: 100vh; }
      .fixed { position: fixed; }
      .top-0 { top: 0; }
      .left-0 { left: 0; }
      .right-0 { right: 0; }
      .z-50 { z-index: 50; }
      .w-full { width: 100%; }
      .h-full { height: 100%; }
      .object-cover { object-fit: cover; }
      .text-white { color: white; }
      .bg-black { background-color: black; }
      .bg-opacity-50 { background-color: rgba(0, 0, 0, 0.5); }
    </style>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
