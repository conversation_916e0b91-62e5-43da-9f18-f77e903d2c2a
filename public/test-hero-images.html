<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Hero Images</title>
    <style>
        body { margin: 0; font-family: <PERSON>l, sans-serif; }
        .hero-section {
            min-height: 100vh;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        }
        .content {
            text-align: center;
            background: rgba(0,0,0,0.5);
            padding: 2rem;
            border-radius: 10px;
        }
        @media (max-width: 767px) {
            .hero-section {
                background-image: url('./images-optimized/wood_texture-hero-mobile.webp');
                filter: blur(0.5px) brightness(0.8) contrast(1.1);
            }
        }
        @media (min-width: 768px) and (max-width: 1023px) {
            .hero-section {
                background-image: url('./images-optimized/wood_texture-hero-tablet.webp');
                filter: blur(0.5px) brightness(0.8) contrast(1.1);
            }
        }
        @media (min-width: 1024px) {
            .hero-section {
                background-image: url('./images-optimized/wood_texture-hero-desktop.webp');
                filter: blur(0.5px) brightness(0.8) contrast(1.1);
            }
        }
    </style>
</head>
<body>
    <div class="hero-section">
        <div class="content">
            <h1>Test Hero Section</h1>
            <p>Nový obrázek bez vodoznaku z Unsplash</p>
            <p>Responzivní pozadí pro různé velikosti obrazovek</p>
            <p>Zjemněná textura s filtry pro lepší čitelnost</p>
            <p>Nové tmavé barevné schéma (slate místo amber)</p>
        </div>
    </div>
</body>
</html>