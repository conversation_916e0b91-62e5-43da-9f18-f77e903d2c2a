import React, { Suspense } from 'react';
import { useTranslation } from 'react-i18next';
import Navbar from './components/Navbar';
import Footer from './components/Footer';
import HeroSection from './components/HeroSection';
import SEO from './components/SEO';
import ScrollToTop from './components/ScrollToTop';
import './i18n';

// Ultra lazy load non-critical components with delay
const AboutSection = React.lazy(() => 
  new Promise<{ default: React.ComponentType<any> }>(resolve => 
    setTimeout(() => resolve(import('./components/AboutSection')), 50)
  )
);
const ServicesSection = React.lazy(() => 
  new Promise<{ default: React.ComponentType<any> }>(resolve => 
    setTimeout(() => resolve(import('./components/ServicesSection')), 100)
  )
);
const PortfolioSection = React.lazy(() => 
  new Promise<{ default: React.ComponentType<any> }>(resolve => 
    setTimeout(() => resolve(import('./components/PortfolioSection')), 150)
  )
);
const MaterialsSection = React.lazy(() => 
  new Promise<{ default: React.ComponentType<any> }>(resolve => 
    setTimeout(() => resolve(import('./components/MaterialsSection')), 200)
  )
);
const ContactSection = React.lazy(() => 
  new Promise<{ default: React.ComponentType<any> }>(resolve => 
    setTimeout(() => resolve(import('./components/ContactSection')), 250)
  )
);

function App() {
  useTranslation();

  return (
    <div className="min-h-screen flex flex-col">
      <SEO />
      <Navbar />
      
      <main className="flex-grow">
        <HeroSection />
        <Suspense fallback={<div className="min-h-screen flex items-center justify-center">Loading...</div>}>
          <AboutSection />
          <ServicesSection />
          <PortfolioSection />
          <MaterialsSection />
          <ContactSection />
        </Suspense>
      </main>
      
      <Footer />
      <ScrollToTop />
    </div>
  );
}

export default App;
