import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';

interface ContactSectionProps {
  className?: string;
}

interface ContactFormData {
  name: string;
  email: string;
  phone?: string;
  message: string;
  consent: boolean;
}

const ContactSection: React.FC<ContactSectionProps> = ({ className }) => {
  const { t } = useTranslation();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<ContactFormData>();

  const onSubmit = async (data: ContactFormData) => {
    setIsSubmitting(true);
    setSubmitStatus('idle');

    try {
      // Sanitize input to prevent injection attacks
      const sanitizedData = {
        name: data.name.replace(/[\r\n]/g, ' ').trim(),
        email: data.email.replace(/[\r\n]/g, ' ').trim(),
        phone: data.phone?.replace(/[\r\n]/g, ' ').trim() || '',
        message: data.message.replace(/[\r\n]/g, ' ').trim()
      };

      // Send to Formspree
      const formspreeEndpoint = import.meta.env.VITE_FORMSPREE_ENDPOINT || 'https://formspree.io/f/YOUR_FORM_ID';
      
      const response = await fetch(formspreeEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: sanitizedData.name,
          email: sanitizedData.email,
          phone: sanitizedData.phone,
          message: sanitizedData.message,
          _subject: `Nová zpráva z webu - ${sanitizedData.name}`,
          _replyto: sanitizedData.email,
        }),
      });

      if (response.ok) {
        setSubmitStatus('success');
        reset();
      } else {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    } catch (error) {
      console.error('Form submission error:', error);
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section id="contact" className={`py-16 bg-amber-50 ${className}`}>
      <div className="container mx-auto px-4">
        <h2 className="text-3xl md:text-4xl font-bold text-amber-900 mb-8 text-center">
          {t('contact.title')}
        </h2>

        <p className="text-center text-gray-700 max-w-3xl mx-auto mb-12">
          {t('contact.intro')}
        </p>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left column - Company name and contact information */}
          <div className="bg-white rounded-lg shadow-md p-8">
            <div className="mb-8">
              <h3 className="text-2xl font-semibold text-amber-800 mb-4">
                Truhlářství Ing. Miloslav Šťastný
              </h3>
              <p className="text-gray-600">
                Specializujeme se na výrobu kvalitního nábytku a dřevěných doplňků z masivního dřeva.
                Ruční práce, zakázková výroba na míru, dlouhá životnost.
              </p>
            </div>

            <h4 className="text-xl font-semibold text-amber-800 mb-6">
              Kontaktní údaje
            </h4>
            <div className="space-y-4 mb-8">
              <div className="flex items-start">
                <div className="text-amber-700 mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                  </svg>
                </div>
                <div>
                  <p className="font-medium">{t('contact.details.phone')}</p>
                  <p className="text-gray-700">+420 602 342 275</p>
                </div>
              </div>

              <div className="flex items-start">
                <div className="text-amber-700 mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
                <div>
                  <p className="font-medium">{t('contact.details.email')}</p>
                  <p className="text-gray-700"><EMAIL></p>
                </div>
              </div>

              <div className="flex items-start">
                <div className="text-amber-700 mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
                <div>
                  <p className="font-medium">{t('contact.details.address')}</p>
                  <p className="text-gray-700">K Lesu 154, 252 18 Ptice</p>
                </div>
              </div>
            </div>

            <h4 className="text-xl font-semibold text-amber-800 mb-4">
              {t('contact.hours.title')}
            </h4>
            <div className="space-y-2">
              <div>
                <span className="font-medium">{t('contact.hours.weekdays')}</span>
              </div>
            </div>
          </div>

          {/* Right column - Contact form */}
          <div className="bg-white rounded-lg shadow-md p-8">
            <h3 className="text-2xl font-semibold text-amber-800 mb-6">
              {t('contact.form.title')}
            </h3>

            {submitStatus === 'success' && (
              <div className="mb-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded-md">
                Děkujeme! Vaše zpráva byla úspěšně odeslána. Odpovíme vám co nejdříve.
              </div>
            )}

            {submitStatus === 'error' && (
              <div className="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded-md">
                Omlouváme se, při odesílání zprávy došlo k chybě. Zkuste to prosím znovu nebo nás kontaktujte pří<NAME_EMAIL> nebo +420 602 342 275.
              </div>
            )}

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              <div>
                <label htmlFor="name" className="block text-gray-700 font-medium mb-1">
                  {t('contact.form.name')} *
                </label>
                <input
                  type="text"
                  id="name"
                  {...register('name', { required: 'Jméno je povinné' })}
                  className={`w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 ${
                    errors.name ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="email" className="block text-gray-700 font-medium mb-1">
                  {t('contact.form.email')} *
                </label>
                <input
                  type="email"
                  id="email"
                  {...register('email', {
                    required: 'Email je povinný',
                    pattern: {
                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                      message: 'Neplatný formát emailu'
                    }
                  })}
                  className={`w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 ${
                    errors.email ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {errors.email && (
                  <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="phone" className="block text-gray-700 font-medium mb-1">
                  {t('contact.form.phone')}
                </label>
                <input
                  type="tel"
                  id="phone"
                  {...register('phone')}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                />
              </div>

              <div>
                <label htmlFor="message" className="block text-gray-700 font-medium mb-1">
                  {t('contact.form.message')} *
                </label>
                <textarea
                  id="message"
                  rows={5}
                  {...register('message', { required: 'Zpráva je povinná' })}
                  className={`w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 ${
                    errors.message ? 'border-red-500' : 'border-gray-300'
                  }`}
                ></textarea>
                {errors.message && (
                  <p className="mt-1 text-sm text-red-600">{errors.message.message}</p>
                )}
              </div>

              <div className="flex items-start">
                <input
                  type="checkbox"
                  id="consent"
                  {...register('consent', { required: 'Souhlas je povinný' })}
                  className="mt-1 mr-2"
                />
                <label htmlFor="consent" className="text-gray-700">
                  {t('contact.form.consent')} *
                </label>
              </div>
              {errors.consent && (
                <p className="text-sm text-red-600">{errors.consent.message}</p>
              )}

              <button
                type="submit"
                disabled={isSubmitting}
                className={`w-full font-bold py-3 px-6 rounded-lg transition-colors ${
                  isSubmitting
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-amber-700 hover:bg-amber-800'
                } text-white`}
              >
                {isSubmitting ? 'Odesílám...' : t('contact.form.submit')}
              </button>
            </form>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;
