import React from 'react';
import { useTranslation } from 'react-i18next';
import ResponsiveBackground from './ResponsiveBackground';

interface HeroSectionProps {
  className?: string;
}

const HeroSection: React.FC<HeroSectionProps> = ({ className }) => {
  const { t } = useTranslation();

  return (
    <ResponsiveBackground 
      src="wood_texture" 
      className="relative min-h-screen flex items-center overflow-hidden"
      isHero={true}
      filter="blur(0.5px) brightness(0.8) contrast(1.1)"
    >
      <section 
        id="home" 
        className={`relative w-full min-h-screen flex items-center ${className}`}
      >
        {/* Dark overlay with subtle blur effect for better text readability */}
        <div className="absolute inset-0 bg-black bg-opacity-60 z-10"></div>
        <div className="absolute inset-0 bg-gradient-to-b from-black/20 via-transparent to-black/40 z-10"></div>

        {/* Content */}
        <div className="container mx-auto px-4 relative z-20 text-white pt-20 md:pt-24">
          <div className="max-w-2xl">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-4">
              {t('home.title')}
            </h1>
            <h2 className="text-xl md:text-2xl mb-6">
              {t('home.subtitle')}
            </h2>
            <p className="text-lg mb-8">
              {t('home.intro')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <a
                href="#contact"
                className="bg-slate-700 hover:bg-slate-600 text-white font-bold py-3 px-6 rounded-lg transition-colors text-center shadow-lg"
              >
                {t('contact.title')}
              </a>
              <a
                href="#portfolio"
                className="bg-transparent hover:bg-white hover:text-slate-800 text-white font-bold py-3 px-6 rounded-lg border-2 border-white transition-colors text-center shadow-lg"
              >
                {t('portfolio.title')}
              </a>
            </div>
          </div>
        </div>
      </section>
    </ResponsiveBackground>
  );
};

export default HeroSection;
