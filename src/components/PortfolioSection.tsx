import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import ResponsiveImage from './ResponsiveImage';
import ImageModal from './ImageModal';

interface PortfolioSectionProps {
  className?: string;
}

interface PortfolioImage {
  filename: string;
  category: string;
  title: {
    cs: string;
    en: string;
  };
  description: {
    cs: string;
    en: string;
  };
}

interface PortfolioData {
  categories: {
    [key: string]: {
      cs: string;
      en: string;
    };
  };
  images: PortfolioImage[];
}

const PortfolioSection: React.FC<PortfolioSectionProps> = React.memo(({ className }) => {
  const { t, i18n } = useTranslation();
  const [activeCategory, setActiveCategory] = useState<string>('all');
  const [portfolioData, setPortfolioData] = useState<PortfolioData | null>(null);
  const [modalImage, setModalImage] = useState<{
    src: string;
    alt: string;
    title: string;
    description: string;
    category: string;
  } | null>(null);

  useEffect(() => {
    // Defer portfolio data loading for better initial performance
    const timer = setTimeout(() => {
      fetch('/images.json')
        .then(response => response.json())
        .then((data: PortfolioData) => setPortfolioData(data))
        .catch(error => console.error('Error loading portfolio data:', error));
    }, 100); // Small delay to prioritize above-the-fold content

    return () => clearTimeout(timer);
  }, []);

  if (!portfolioData) {
    return <div>Loading...</div>;
  }

  const currentLanguage = i18n.language as 'cs' | 'en';

  // Get categories for current language
  const categories = Object.entries(portfolioData.categories).map(
    ([key, value]) => ({ id: key, name: value[currentLanguage] })
  );

  // Filter items based on active category
  const filteredItems = activeCategory === 'all'
    ? portfolioData.images
    : portfolioData.images.filter(item => item.category === activeCategory);

  // Function to open modal with image details
  const openModal = (item: PortfolioImage) => {
    setModalImage({
      src: item.filename,
      alt: item.title[currentLanguage],
      title: item.title[currentLanguage],
      description: item.description[currentLanguage],
      category: portfolioData.categories[item.category][currentLanguage]
    });
  };

  // Function to close modal
  const closeModal = () => {
    setModalImage(null);
  };

  return (
    <section id="portfolio" className={`py-16 bg-amber-50 ${className}`}>
      <div className="container mx-auto px-4">
        <h2 className="text-3xl md:text-4xl font-bold text-amber-900 mb-8 text-center">
          {t('portfolio.title')}
        </h2>

        <p className="text-center text-gray-700 max-w-3xl mx-auto mb-8">
          {t('portfolio.intro')}
        </p>

        {/* Category filters */}
        <div className="flex flex-wrap justify-center gap-2 mb-8">
          <button
            onClick={() => setActiveCategory('all')}
            className={`px-4 py-2 rounded-full ${
              activeCategory === 'all'
                ? 'bg-amber-700 text-white'
                : 'bg-white text-amber-900 border border-amber-700'
            }`}
          >
            {currentLanguage === 'cs' ? 'Vše' : 'All'}
          </button>

          {categories.map(category => (
            <button
              key={category.id}
              onClick={() => setActiveCategory(category.id)}
              className={`px-4 py-2 rounded-full ${
                activeCategory === category.id
                  ? 'bg-amber-700 text-white'
                  : 'bg-white text-amber-900 border border-amber-700'
              }`}
            >
              {category.name}
            </button>
          ))}
        </div>

        {/* Portfolio grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredItems.map((item, index) => (
            <div
              key={index}
              className="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-shadow cursor-pointer"
              onClick={() => openModal(item)}
            >
              <div className="aspect-[4/3] overflow-hidden relative">
                <ResponsiveImage
                  src={item.filename}
                  alt={item.title[currentLanguage]}
                  className="w-full h-full object-cover transition-transform hover:scale-105 absolute inset-0"
                  loading={index < 2 ? "eager" : "lazy"}
                  fetchPriority={index === 0 ? "high" : "low"}
                />
              </div>
              <div className="p-4">
                <h3 className="text-lg font-semibold text-amber-900">{item.title[currentLanguage]}</h3>
                <p className="text-sm text-gray-600 mt-1">
                  {portfolioData.categories[item.category][currentLanguage]}
                </p>
                <p className="text-sm text-gray-500 mt-2">
                  {item.description[currentLanguage]}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Image Modal */}
      {modalImage && (
        <ImageModal
          isOpen={!!modalImage}
          onClose={closeModal}
          imageSrc={modalImage.src}
          imageAlt={modalImage.alt}
          title={modalImage.title}
          description={modalImage.description}
          category={modalImage.category}
        />
      )}
    </section>
  );
});

export default PortfolioSection;
