import React from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';

interface SeoProps {
  title?: string;
  description?: string;
  keywords?: string;
  canonicalUrl?: string;
  ogImage?: string;
  ogType?: string;
  twitterCard?: string;
}

const SEO: React.FC<SeoProps> = ({
  title,
  description,
  keywords,
  canonicalUrl,
  ogImage,
  ogType = 'website',
  twitterCard = 'summary_large_image'
}) => {
  const { i18n } = useTranslation();
  const currentLanguage = i18n.language;

  // Default values based on language
  const defaultTitle = currentLanguage === 'cs'
    ? 'Truhlářství Ing. <PERSON>slav <PERSON> - Kvalitní nábytek z masivního dřeva na míru'
    : 'Carpentry Ing. <PERSON>slav <PERSON> - Quality Custom Solid Wood Furniture';

  const defaultDescription = currentLanguage === 'cs'
    ? 'Truhlářství Ing. <PERSON><PERSON> - výroba kvalitního nábytku a dřevěn<PERSON><PERSON> doplňk<PERSON> z masivního dřeva. Ruč<PERSON><PERSON> pr<PERSON>, zakázková výroba na míru, dlouhá životnost.'
    : 'Carpentry Ing. Miloslav Šťastný - manufacturing quality furniture and wooden accessories from solid wood. Handcrafted, custom-made, long-lasting.';

  const defaultKeywords = currentLanguage === 'cs'
    ? 'truhlářství, <PERSON>slav Šťastný, nábytek na míru, masivní dřevo, zakázková výroba, kvalitní nábytek, dřevěné doplňky, Ptice'
    : 'carpentry, Miloslav Šťastný, custom furniture, solid wood, custom manufacturing, quality furniture, wooden accessories';

  const siteUrl = 'https://ms-truhlar.cz';
  const pageTitle = title || defaultTitle;
  const pageDescription = description || defaultDescription;
  const pageKeywords = keywords || defaultKeywords;
  const pageCanonicalUrl = canonicalUrl || `${siteUrl}${currentLanguage === 'en' ? '/en' : ''}`;
  const pageOgImage = ogImage || `${siteUrl}/images/wood_texture.jpeg`;

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <html lang={currentLanguage} />
      <title>{pageTitle}</title>
      <meta name="description" content={pageDescription} />
      <meta name="keywords" content={pageKeywords} />

      {/* Canonical URL */}
      <link rel="canonical" href={pageCanonicalUrl} />

      {/* Open Graph / Facebook */}
      <meta property="og:type" content={ogType} />
      <meta property="og:url" content={pageCanonicalUrl} />
      <meta property="og:title" content={pageTitle} />
      <meta property="og:description" content={pageDescription} />
      <meta property="og:image" content={pageOgImage} />

      {/* Twitter */}
      <meta name="twitter:card" content={twitterCard} />
      <meta name="twitter:url" content={pageCanonicalUrl} />
      <meta name="twitter:title" content={pageTitle} />
      <meta name="twitter:description" content={pageDescription} />
      <meta name="twitter:image" content={pageOgImage} />

      {/* Alternate Language Links */}
      <link
        rel="alternate"
        hrefLang="cs"
        href={`${siteUrl}`}
      />
      <link
        rel="alternate"
        hrefLang="en"
        href={`${siteUrl}/en`}
      />
      <link
        rel="alternate"
        hrefLang="x-default"
        href={`${siteUrl}`}
      />

      {/* Structured Data - Local Business */}
      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "LocalBusiness",
          "@id": `${siteUrl}/#business`,
          "name": "Truhlářství Ing. Miloslav Šťastný",
          "alternateName": "Truhlářství Šťastný",
          "description": pageDescription,
          "url": siteUrl,
          "telephone": "+420602342275",
          "email": "<EMAIL>",
          "address": {
            "@type": "PostalAddress",
            "streetAddress": "K Lesu 154",
            "addressLocality": "Ptice",
            "postalCode": "252 18",
            "addressCountry": "CZ"
          },
          "geo": {
            "@type": "GeoCoordinates",
            "latitude": "49.9167",
            "longitude": "14.4167"
          },
          "openingHours": "Mo-Sa 08:00-18:00",
          "priceRange": "$$",
          "serviceArea": {
            "@type": "GeoCircle",
            "geoMidpoint": {
              "@type": "GeoCoordinates",
              "latitude": "49.9167",
              "longitude": "14.4167"
            },
            "geoRadius": "50000"
          },
          "hasOfferCatalog": {
            "@type": "OfferCatalog",
            "name": "Truhlářské služby",
            "itemListElement": [
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Service",
                  "name": "Nábytek na zakázku",
                  "description": "Výroba nábytku na míru z masivního dřeva"
                }
              },
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Service",
                  "name": "Vestavěné skříně",
                  "description": "Návrh a výroba vestavěných skříní"
                }
              },
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Service",
                  "name": "Kuchyňské linky",
                  "description": "Výroba kuchyňských linek na míru"
                }
              },
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Service",
                  "name": "Renovace nábytku",
                  "description": "Renovace a oprava starého nábytku"
                }
              }
            ]
          },
          "founder": {
            "@type": "Person",
            "name": "Ing. Miloslav Šťastný",
            "jobTitle": "Truhlář"
          },
          "image": `${siteUrl}/images/wood_texture.jpeg`,
          "logo": `${siteUrl}/images/wood_texture.jpeg`,
          "sameAs": []
        })}
      </script>

      {/* Structured Data - Website */}
      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "WebSite",
          "@id": `${siteUrl}/#website`,
          "url": siteUrl,
          "name": "Truhlářství Ing. Miloslav Šťastný",
          "description": pageDescription,
          "publisher": {
            "@id": `${siteUrl}/#business`
          },
          "inLanguage": currentLanguage,
          "potentialAction": {
            "@type": "SearchAction",
            "target": {
              "@type": "EntryPoint",
              "urlTemplate": `${siteUrl}/?q={search_term_string}`
            },
            "query-input": "required name=search_term_string"
          }
        })}
      </script>

      {/* Google Analytics - Add your GA4 tracking ID here */}
      {/* 
      <script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
      <script>
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', 'GA_MEASUREMENT_ID');
        `}
      </script>
      */}
    </Helmet>
  );
};

export default SEO;
