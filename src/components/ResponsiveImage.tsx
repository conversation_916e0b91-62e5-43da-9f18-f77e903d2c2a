import React from 'react';

interface ResponsiveImageProps {
  src: string; // Original image path (e.g., "wood_texture.jpeg")
  alt: string;
  className?: string;
  loading?: 'lazy' | 'eager';
  fetchPriority?: 'high' | 'low' | 'auto';
  isHero?: boolean; // Special handling for hero images
  width?: number; // Explicit width for CLS prevention
  height?: number; // Explicit height for CLS prevention
  aspectRatio?: string; // CSS aspect ratio (e.g., "16/9", "4/3")
}

const ResponsiveImage: React.FC<ResponsiveImageProps> = ({
  src,
  alt,
  className = '',
  loading = 'lazy',
  fetchPriority = 'auto',
  isHero = false,
  width,
  height,
  aspectRatio
}) => {
  // Extract filename without extension
  const getImageName = (path: string) => {
    const filename = path.split('/').pop() || '';
    return filename.replace(/\.[^/.]+$/, '');
  };

  // Extract directory path
  const getImageDir = (path: string) => {
    const parts = path.split('/');
    parts.pop(); // Remove filename
    return parts.join('/');
  };

  const imageName = getImageName(src);
  const imageDir = getImageDir(src);
  const basePath = imageDir ? `images-optimized/${imageDir}` : 'images-optimized';

  // Create style object for dimensions and aspect ratio
  const imageStyle: React.CSSProperties = {};
  if (width) imageStyle.width = width;
  if (height) imageStyle.height = height;
  if (aspectRatio) imageStyle.aspectRatio = aspectRatio;

  if (isHero) {
    const heroDesktopPath = `/${basePath}/${imageName}-hero-desktop.webp`.replace(/ /g, '%20');
    const heroTabletPath = `/${basePath}/${imageName}-hero-tablet.webp`.replace(/ /g, '%20');
    const heroMobilePath = `/${basePath}/${imageName}-hero-mobile.webp`.replace(/ /g, '%20');

    return (
      <picture className={className} style={imageStyle}>
        {/* Desktop - largest first */}
        <source
          media="(min-width: 1024px)"
          srcSet={heroDesktopPath}
          type="image/webp"
        />
        {/* Tablet */}
        <source
          media="(min-width: 768px) and (max-width: 1023px)"
          srcSet={heroTabletPath}
          type="image/webp"
        />
        {/* Mobile */}
        <source
          media="(max-width: 767px)"
          srcSet={heroMobilePath}
          type="image/webp"
        />
        {/* Fallback */}
        <img
          src={`/images/${src}`}
          alt={alt}
          className="w-full h-full object-cover"
          loading={loading}
          fetchPriority={fetchPriority}
          width={width}
          height={height}
          decoding="async"
        />
      </picture>
    );
  }

  // Encode URLs properly for spaces and special characters
  const desktopPath = `/${basePath}/${imageName}-desktop.webp`.replace(/ /g, '%20');
  const tabletPath = `/${basePath}/${imageName}-tablet.webp`.replace(/ /g, '%20');
  const mobilePath = `/${basePath}/${imageName}-mobile.webp`.replace(/ /g, '%20');

  return (
    <picture className={className} style={imageStyle}>
      {/* Desktop - largest first */}
      <source
        media="(min-width: 1024px)"
        srcSet={desktopPath}
        type="image/webp"
      />
      {/* Tablet */}
      <source
        media="(min-width: 768px) and (max-width: 1023px)"
        srcSet={tabletPath}
        type="image/webp"
      />
      {/* Mobile */}
      <source
        media="(max-width: 767px)"
        srcSet={mobilePath}
        type="image/webp"
      />
      {/* Fallback */}
      <img
        src={`/images/${src}`}
        alt={alt}
        className="w-full h-full object-cover"
        loading={loading}
        fetchPriority={fetchPriority}
        width={width}
        height={height}
        decoding="async"
      />
    </picture>
  );
};

export default ResponsiveImage;
