import React, { useEffect, useState } from 'react';
import ResponsiveImage from './ResponsiveImage';

interface ImageModalProps {
  isOpen: boolean;
  onClose: () => void;
  imageSrc: string;
  imageAlt: string;
  title: string;
  description: string;
  category: string;
}

const ImageModal: React.FC<ImageModalProps> = ({
  isOpen,
  onClose,
  imageSrc,
  imageAlt,
  title,
  description,
  category
}) => {
  const [useOriginalImage, setUseOriginalImage] = useState(true);

  // Reset image state when modal opens with new image
  useEffect(() => {
    if (isOpen) {
      setUseOriginalImage(true);
    }
  }, [isOpen, imageSrc]);

  // Close modal on Escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-2 sm:p-4"
      onClick={onClose}
      role="dialog"
      aria-modal="true"
      aria-labelledby="modal-title"
    >
      <div
        className="bg-white rounded-lg shadow-2xl flex flex-col"
        onClick={(e) => e.stopPropagation()}
        style={{ 
          maxWidth: '98vw',
          maxHeight: '98vh',
          width: 'fit-content',
          height: 'fit-content'
        }}
      >
        {/* Header */}
        <div className="flex justify-between items-center p-3 border-b border-gray-200 flex-shrink-0">
          <div>
            <h3 id="modal-title" className="text-lg font-semibold text-amber-900">{title}</h3>
            <p className="text-sm text-gray-600">{category}</p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 text-2xl font-bold w-8 h-8 flex items-center justify-center"
            aria-label="Zavřít"
          >
            ×
          </button>
        </div>

        {/* Image Container - dynamically sized to fit image */}
        <div className="flex items-center justify-center p-2 bg-gray-50">
          <div className="flex items-center justify-center">
            {useOriginalImage ? (
              /* Try original high-quality image first */
              <img
                src={`/images/${imageSrc}`}
                alt={imageAlt}
                className="rounded shadow-sm"
                loading="eager"
                style={{
                  maxWidth: 'calc(98vw - 1rem)',
                  maxHeight: description ? 'calc(98vh - 10rem)' : 'calc(98vh - 7rem)', // Account for header and optional description
                  width: 'auto',
                  height: 'auto',
                  objectFit: 'contain'
                }}
                onError={() => {
                  console.warn(`Failed to load original image: ${imageSrc}, falling back to optimized version`);
                  setUseOriginalImage(false);
                }}
              />
            ) : (
              /* Fallback to optimized ResponsiveImage */
              <div
                style={{
                  maxWidth: 'calc(98vw - 1rem)',
                  maxHeight: description ? 'calc(98vh - 10rem)' : 'calc(98vh - 7rem)',
                  width: 'auto',
                  height: 'auto'
                }}
              >
                <ResponsiveImage
                  src={imageSrc}
                  alt={imageAlt}
                  className="rounded shadow-sm w-full h-full object-contain"
                  loading="eager"
                />
              </div>
            )}
          </div>
        </div>

        {/* Description - only show if exists and make it more compact */}
        {description && (
          <div className="p-3 border-t border-gray-200 flex-shrink-0 bg-gray-50">
            <p className="text-gray-700 text-sm">{description}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ImageModal;
