import React from 'react';
import { useTranslation } from 'react-i18next';
import ResponsiveImage from './ResponsiveImage';

interface ServicesSectionProps {
  className?: string;
}

const ServicesSection: React.FC<ServicesSectionProps> = React.memo(({ className }) => {
  const { t } = useTranslation();

  const services = [
    {
      id: 'furniture',
      title: t('services.furniture_title'),
      description: t('services.furniture'),
      image: 'Nabytek na zakazku/knihovna_dub.jpg'
    },
    {
      id: 'accessories',
      title: t('services.accessories_title'),
      description: t('services.accessories'),
      image: 'Doplnky/policka.jpg'
    },
    {
      id: 'wardrobes',
      title: t('services.wardrobes_title'),
      description: t('services.wardrobes'),
      image: 'Vestavene skrine/skrin_podkrovi.jpg'
    },
    {
      id: 'kitchen',
      title: t('services.kitchen_title'),
      description: t('services.kitchen'),
      image: '<PERSON>chyns<PERSON> skrinky/kuchyn_zavesna_skrinka_buk.jpg'
    },
    {
      id: 'renovation',
      title: t('services.renovation_title'),
      description: t('services.renovation'),
      image: 'Renovace/kuchyn_vymena_dvirek.jpg'
    }
  ];

  return (
    <section id="services" className={`py-16 ${className}`}>
      <div className="container mx-auto px-4">
        <h2 className="text-3xl md:text-4xl font-bold text-amber-900 mb-8 text-center">
          {t('services.title')}
        </h2>

        <div className="space-y-12">
          {services.map((service, index) => (
            <div
              key={service.id}
              className={`flex flex-col ${index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'} gap-8 items-center`}
            >
              <div className="md:w-1/2">
                <div className="aspect-[3/2] overflow-hidden rounded-lg shadow-lg relative">
                  <ResponsiveImage
                    src={service.image}
                    alt={service.title}
                    className="w-full h-full object-cover absolute inset-0"
                    loading={index < 1 ? "eager" : "lazy"}
                    fetchPriority={index === 0 ? "high" : "low"}
                  />
                </div>
              </div>
              <div className="md:w-1/2">
                <h3 className="text-2xl font-semibold text-amber-800 mb-4">
                  {service.title}
                </h3>
                <p className="text-gray-700">
                  {service.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
});

export default ServicesSection;
