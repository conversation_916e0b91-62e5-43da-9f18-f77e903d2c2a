import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import LanguageSwitcher from './LanguageSwitcher';
import ResponsiveBackground from './ResponsiveBackground';

interface NavbarProps {
  className?: string;
}

const Navbar: React.FC<NavbarProps> = ({ className }) => {
  const { t } = useTranslation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  return (
    <ResponsiveBackground src="dub_1.jpg" className={`fixed top-0 left-0 right-0 z-50 text-white py-4 ${className}`}>
      {/* Tmavý overlay pro sladění s novým hero obrázkem */}
      <div className="absolute inset-0 bg-slate-800 bg-opacity-95"></div>

      {/* Jemný gradient */}
      <div className="absolute inset-0 bg-gradient-to-r from-slate-900/10 via-transparent to-slate-900/10"></div>

      <nav className="relative z-10">

      <div className="relative z-10 container mx-auto px-4 flex justify-between items-center">
        <div className="text-xl font-bold">Truhlářství Ing. Miloslav Šťastný</div>

        <div className="hidden md:flex space-x-6">
          <a href="#home" className="hover:text-slate-200 transition-colors">
            {t('navigation.home')}
          </a>
          <a href="#about" className="hover:text-slate-200 transition-colors">
            {t('navigation.about')}
          </a>
          <a href="#services" className="hover:text-slate-200 transition-colors">
            {t('navigation.services')}
          </a>
          <a href="#portfolio" className="hover:text-slate-200 transition-colors">
            {t('navigation.portfolio')}
          </a>
          <a href="#materials" className="hover:text-slate-200 transition-colors">
            {t('navigation.materials')}
          </a>
          <a href="#contact" className="hover:text-slate-200 transition-colors">
            {t('navigation.contact')}
          </a>
        </div>

        <div className="flex items-center space-x-4">
          <LanguageSwitcher />

          {/* Mobile menu button */}
          <button
            className="md:hidden text-white focus:outline-none"
            onClick={toggleMobileMenu}
            aria-label="Toggle mobile menu"
          >
            {isMobileMenuOpen ? (
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            ) : (
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            )}
          </button>
        </div>
      </div>
      </nav>

      {/* Mobile menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden relative z-20">
          <div className="absolute inset-0 bg-slate-800 bg-opacity-95"></div>
          <div className="relative z-10 px-4 pt-2 pb-4 space-y-2">
            <a
              href="#home"
              className="block py-2 text-white hover:text-slate-200 transition-colors"
              onClick={closeMobileMenu}
            >
              {t('navigation.home')}
            </a>
            <a
              href="#about"
              className="block py-2 text-white hover:text-slate-200 transition-colors"
              onClick={closeMobileMenu}
            >
              {t('navigation.about')}
            </a>
            <a
              href="#services"
              className="block py-2 text-white hover:text-slate-200 transition-colors"
              onClick={closeMobileMenu}
            >
              {t('navigation.services')}
            </a>
            <a
              href="#portfolio"
              className="block py-2 text-white hover:text-slate-200 transition-colors"
              onClick={closeMobileMenu}
            >
              {t('navigation.portfolio')}
            </a>
            <a
              href="#materials"
              className="block py-2 text-white hover:text-slate-200 transition-colors"
              onClick={closeMobileMenu}
            >
              {t('navigation.materials')}
            </a>
            <a
              href="#contact"
              className="block py-2 text-white hover:text-slate-200 transition-colors"
              onClick={closeMobileMenu}
            >
              {t('navigation.contact')}
            </a>
          </div>
        </div>
      )}
    </ResponsiveBackground>
  );
};

export default Navbar;
