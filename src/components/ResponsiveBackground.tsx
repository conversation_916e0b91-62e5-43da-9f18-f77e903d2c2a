import React, { useEffect, useState } from 'react';

interface ResponsiveBackgroundProps {
  src: string; // Original image path (e.g., "dub_1.jpg")
  className?: string;
  children?: React.ReactNode;
  isHero?: boolean; // Special handling for hero images
  filter?: string; // CSS filter for background image
}

const ResponsiveBackground: React.FC<ResponsiveBackgroundProps> = ({
  src,
  className = '',
  children,
  isHero = false,
  filter = ''
}) => {
  const [currentImagePath, setCurrentImagePath] = useState('');

  // Extract filename without extension
  const getImageName = (path: string) => {
    const filename = path.split('/').pop() || '';
    return filename.replace(/\.[^/.]+$/, '');
  };

  const imageName = getImageName(src);

  // Encode URLs properly for spaces and special characters
  const suffix = isHero ? '-hero' : '';
  const desktopPath = `/images-optimized/${imageName}${suffix}-desktop.webp`.replace(/ /g, '%20');
  const tabletPath = `/images-optimized/${imageName}${suffix}-tablet.webp`.replace(/ /g, '%20');
  const mobilePath = `/images-optimized/${imageName}${suffix}-mobile.webp`.replace(/ /g, '%20');

  useEffect(() => {
    const updateImagePath = () => {
      if (window.innerWidth >= 1024) {
        setCurrentImagePath(desktopPath);
      } else if (window.innerWidth >= 768) {
        setCurrentImagePath(tabletPath);
      } else {
        setCurrentImagePath(mobilePath);
      }
    };

    // Set initial image
    updateImagePath();

    // Listen for window resize
    window.addEventListener('resize', updateImagePath);

    return () => window.removeEventListener('resize', updateImagePath);
  }, [desktopPath, tabletPath, mobilePath]);

  return (
    <div className={`relative ${className}`}>
      <div
        className="absolute inset-0"
        style={{
          backgroundImage: `url('${currentImagePath}')`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          backgroundAttachment: 'scroll',
          filter: filter
        }}
      ></div>

      {children}
    </div>
  );
};

export default ResponsiveBackground;
