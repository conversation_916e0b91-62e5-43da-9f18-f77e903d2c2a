import React from 'react';
import { useTranslation } from 'react-i18next';
import ResponsiveBackground from './ResponsiveBackground';

interface FooterProps {
  className?: string;
}

const Footer: React.FC<FooterProps> = ({ className }) => {
  const { t } = useTranslation();

  return (
    <ResponsiveBackground src="orech_1.jpg" className={`text-white py-8 ${className}`}>
      {/* Tmavý overlay pro sladění s novým hero obrázkem */}
      <div className="absolute inset-0 bg-slate-900 bg-opacity-90"></div>

      {/* Jemný gradient */}
      <div className="absolute inset-0 bg-gradient-to-t from-slate-950/20 via-transparent to-slate-800/10"></div>

      <footer className="relative z-10">

      <div className="relative z-10 container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div>
            <h3 className="text-xl font-semibold mb-4">Truhlářství Ing. <PERSON></h3>
            <p className="mb-4">
              {t('home.subtitle')}
            </p>
          </div>

          <div>
            <h3 className="text-xl font-semibold mb-4">{t('contact.title')}</h3>
            <p className="mb-2">
              <strong>{t('contact.details.phone')}:</strong> +420 602 342 275
            </p>
            <p className="mb-2">
              <strong>{t('contact.details.email')}:</strong> <EMAIL>
            </p>
            <p>
              <strong>{t('contact.details.address')}:</strong> K Lesu 154, 252 18 Ptice
            </p>
          </div>

          <div>
            <h3 className="text-xl font-semibold mb-4">{t('contact.hours.title')}</h3>
            <p>
              <strong>{t('contact.hours.weekdays')}</strong>
            </p>
          </div>
        </div>

        <div className="border-t border-slate-600 mt-8 pt-6 text-center">
          <p>{t('footer.copyright')}</p>
        </div>
      </div>
      </footer>
    </ResponsiveBackground>
  );
};

export default Footer;
